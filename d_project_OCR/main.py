import torch
import time
from PIL import Image
from transformers import <PERSON>Tokenizer, AutoProcessor, AutoModelForImageTextToText
import fitz  # PyMuPDF for PDF handling
import os
import re


class DocumentOCR:
    def __init__(self, model_path: str, use_cuda: bool = True):
        """Initialize OCR model"""
        self.device = torch.device("cuda" if use_cuda and torch.cuda.is_available() else "cpu")
        print(f"🔧 Using device: {self.device}")

        self.model = AutoModelForImageTextToText.from_pretrained(
            model_path,
            torch_dtype=torch.float16 if self.device.type == "cuda" else torch.float32,
            device_map=None,
            local_files_only=True,
            trust_remote_code=True
        ).to(self.device)
        self.model.eval()

        self.tokenizer = AutoTokenizer.from_pretrained(model_path, local_files_only=True, trust_remote_code=True)
        self.processor = AutoProcessor.from_pretrained(model_path, local_files_only=True, trust_remote_code=True)

    def clean_html_tags(self, text: str) -> str:
        """Remove any HTML/XML tags that might appear in the output"""
        # Remove common HTML table tags
        html_patterns = [
            r'</?table[^>]*>',
            r'</?thead[^>]*>',
            r'</?tbody[^>]*>',
            r'</?tr[^>]*>',
            r'</?td[^>]*>',
            r'</?th[^>]*>',
            r'</?div[^>]*>',
            r'</?span[^>]*>',
            r'</?p[^>]*>',
            r'</?br[^>]*>',
            r'</?ul[^>]*>',
            r'</?ol[^>]*>',
            r'</?li[^>]*>',
            r'</?h[1-6][^>]*>',
            r'</?strong[^>]*>',
            r'</?em[^>]*>',
            r'</?b[^>]*>',
            r'</?i[^>]*>',
            r'<[^>]+>'  # Catch any remaining tags
        ]

        cleaned_text = text
        for pattern in html_patterns:
            cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.IGNORECASE)

        return cleaned_text

    def format_table_text(self, text: str) -> str:
        """Format text to ensure proper table alignment using spaces"""
        lines = text.split('\n')
        formatted_lines = []

        # Simple approach: look for table data patterns and format them
        i = 0
        in_table = False

        while i < len(lines):
            line = lines[i].strip()

            # Skip empty lines
            if not line:
                formatted_lines.append('')
                i += 1
                continue

            # Detect table start
            if not in_table and line.lower() == 'description':
                # Look ahead to see if next few lines are table headers
                if (i + 3 < len(lines) and
                    lines[i + 1].strip().lower() == 'qty' and
                    lines[i + 2].strip().lower() == 'price' and
                    lines[i + 3].strip().lower() == 'total'):

                    in_table = True
                    # Add formatted table header
                    formatted_lines.append(f"{'Description':<30} {'Qty':>5} {'Price':>12} {'Total':>12}")
                    i += 4  # Skip the individual header lines
                    continue

            # Process table data rows
            if in_table:
                # Look for table data pattern: description, qty, price, total in sequence
                if (i + 3 < len(lines) and
                    lines[i + 1].strip().isdigit() and  # qty is a number
                    '$' in lines[i + 2].strip() and     # price has $
                    '$' in lines[i + 3].strip()):       # total has $

                    description = line
                    qty = lines[i + 1].strip()
                    price = lines[i + 2].strip()
                    total = lines[i + 3].strip()

                    formatted_row = f"{description:<30} {qty:>5} {price:>12} {total:>12}"
                    formatted_lines.append(formatted_row)
                    i += 4  # Skip the processed lines
                    continue
                else:
                    # End of table
                    in_table = False

            # Regular line
            formatted_lines.append(line)
            i += 1

        return '\n'.join(formatted_lines)

    def preserve_spatial_layout(self, text: str) -> str:
        """Enhanced spatial layout preservation with intelligent post-processing"""
        lines = text.split('\n')
        formatted_lines = []

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # Skip empty lines
            if not line:
                formatted_lines.append('')
                i += 1
                continue

            # Check if this might be part of a broken layout that needs fixing
            if self._needs_layout_correction(lines, i):
                corrected_lines, next_i = self._correct_layout_section(lines, i)
                formatted_lines.extend(corrected_lines)
                i = next_i
                continue

            # Regular line processing
            formatted_lines.append(line)
            i += 1

        return '\n'.join(formatted_lines)

    def _needs_layout_correction(self, lines: list, start_idx: int) -> bool:
        """Check if this section needs layout correction"""
        # Look for patterns that suggest broken layout
        current_line = lines[start_idx].strip().lower()

        # Check for invoice header section that might be broken
        if any(keyword in current_line for keyword in ['bill to', 'invoice no', 'invoice date', 'due date']):
            return True

        # Check for table headers that might be broken
        if any(keyword in current_line for keyword in ['sl.', 'description', 'qty', 'rate', 'amount']):
            return True

        return False

    def _correct_layout_section(self, lines: list, start_idx: int) -> tuple:
        """Correct layout for a specific section"""
        formatted_lines = []
        i = start_idx

        # Collect lines that belong to this section
        section_lines = []
        while i < len(lines) and i < start_idx + 20:  # Limit section size
            line = lines[i].strip()
            if line:
                section_lines.append(line)
            i += 1

            # Stop at natural breaks
            if line.lower().startswith(('subtotal', 'total', 'authorized', 'signature')):
                break

        # Apply specific corrections based on content
        if any('bill to' in line.lower() for line in section_lines[:3]):
            formatted_lines.extend(self._fix_invoice_header_layout(section_lines))
        elif any('sl.' in line.lower() or 'description' in line.lower() for line in section_lines[:3]):
            formatted_lines.extend(self._fix_table_layout(section_lines))
        else:
            # Default: just clean up the lines
            formatted_lines.extend(section_lines)

        return formatted_lines, i

    def _fix_invoice_header_layout(self, section_lines: list) -> list:
        """Fix invoice header layout to be side-by-side"""
        formatted_lines = []

        # Separate left and right content
        left_content = []
        right_content = []

        for line in section_lines:
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in ['invoice no', 'invoice date', 'due date']):
                right_content.append(line)
            elif any(keyword in line_lower for keyword in ['bill to', 'green1', 'car street', 'city park', 'honk kong']):
                left_content.append(line)

        # Format side-by-side
        max_lines = max(len(left_content), len(right_content))
        for j in range(max_lines):
            left_text = left_content[j] if j < len(left_content) else ""
            right_text = right_content[j] if j < len(right_content) else ""

            if left_text and right_text:
                formatted_line = f"{left_text:<40}{right_text}"
                formatted_lines.append(formatted_line)
            elif left_text:
                formatted_lines.append(left_text)
            elif right_text:
                formatted_line = f"{'':<40}{right_text}"
                formatted_lines.append(formatted_line)

        return formatted_lines

    def _fix_table_layout(self, section_lines: list) -> list:
        """Fix table layout to be properly aligned"""
        formatted_lines = []

        # Add proper table header
        formatted_lines.append(f"{'Sl.':<5} {'Description':<30} {'Qty':<5} {'Rate':<12} {'Amount':<12}")
        formatted_lines.append("")  # Add spacing after header

        # Process the broken table data
        # The data appears to be: number, description, qty, rate, amount in separate lines
        i = 0
        while i < len(section_lines):
            line = section_lines[i].strip()

            # Skip header lines
            if any(keyword in line.lower() for keyword in ['sl.', 'description', 'qty', 'rate', 'amount']):
                i += 1
                continue

            # Look for row number (single digit)
            if line.isdigit() and len(line) == 1:
                row_num = line
                description = section_lines[i + 1] if i + 1 < len(section_lines) else ""
                qty = section_lines[i + 2] if i + 2 < len(section_lines) else ""
                rate = section_lines[i + 3] if i + 3 < len(section_lines) else ""
                amount = section_lines[i + 4] if i + 4 < len(section_lines) else ""

                # Format the row with proper alignment
                formatted_row = f"{row_num:<5} {description:<30} {qty:<5} {rate:<12} {amount:<12}"
                formatted_lines.append(formatted_row)
                i += 5  # Skip the processed lines
            else:
                i += 1

        return formatted_lines





    def detect_document_layout(self, text: str) -> str:
        """Coordinate-based document layout processing"""
        return self.preserve_spatial_layout(text)

    def extract_document_text_smart(self, image: Image.Image, page_num: int = 0) -> str:
        """Enhanced OCR for documents"""
        try:
            start_time = time.time()
            print(f"\n[INFO] OCR processing page {page_num + 1}...")

            prompt = (
                "You are an OCR system that MUST preserve the exact visual layout of documents. "
                "SPATIAL POSITIONING REQUIREMENTS:\n"
                "1. SCAN the image like a grid from LEFT to RIGHT, TOP to BOTTOM\n"
                "2. For EACH HORIZONTAL ROW, identify ALL text elements and their positions\n"
                "3. If you see text on the LEFT side and text on the RIGHT side of the same row:\n"
                "   Format as: Left Text[SPACES]Right Text\n"
                "   Use enough spaces to match the visual gap you see\n"
                "4. For TABLES: Preserve exact column alignment using spaces\n"
                "   Example: Sl.    Description         Qty    Rate      Amount\n"
                "            1      Desktop furniture   1      $232.00   $232.00\n"
                "5. CRITICAL: If invoice details appear on the right side, format like:\n"
                "   Company Info                        Invoice No : INV-005\n"
                "   Address Line                        Invoice Date : Jun 22, 2021\n"
                "6. Count the visual spacing and use approximately that many spaces\n"
                "7. NEVER output text sequentially if it appears side-by-side in the image\n"
                "8. Output ONLY plain text with proper spacing - NO markup tags\n"
                "9. Think: 'Where exactly does each piece of text appear on the page?'\n"
                "10. Maintain the visual structure exactly as you see it"
            )

            messages = [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": [
                    {"type": "image", "image": image},
                    {"type": "text", "text": prompt},
                ]},
            ]

            text = self.processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
            inputs = self.processor(
                text=[text], images=[image], padding=True, return_tensors="pt"
            ).to(self.model.device)

            output_ids = self.model.generate(**inputs, max_new_tokens=15000, do_sample=False)
            generated_ids = [output_ids[len(input_ids):] for input_ids, output_ids in zip(inputs.input_ids, output_ids)]
            output_text = self.processor.batch_decode(generated_ids, skip_special_tokens=True, clean_up_tokenization_spaces=True)

            # Post-process the output to ensure clean plain text with spatial layout preservation
            raw_text = output_text[0]

            # Step 1: Remove any HTML/XML tags
            cleaned_text = self.clean_html_tags(raw_text)

            # Step 2: Detect document type and preserve spatial layout
            spatial_text = self.detect_document_layout(cleaned_text)

            # Step 3: Format table-like content for better alignment
            formatted_text = self.format_table_text(spatial_text)

            # Step 4: Clean up excessive whitespace while preserving intentional spacing
            lines = formatted_text.split('\n')
            processed_lines = []
            for line in lines:
                # Remove trailing whitespace but preserve leading whitespace (indentation)
                processed_line = line.rstrip()
                processed_lines.append(processed_line)

            final_text = '\n'.join(processed_lines)

            # Remove excessive consecutive empty lines (more than 2)
            final_text = re.sub(r'\n{4,}', '\n\n\n', final_text)

            execution_time = time.time() - start_time
            print(f"[INFO] ✅ OCR completed in {execution_time:.2f} seconds")

            return final_text

        except Exception as e:
            print(f"❌ Error in OCR (page {page_num + 1}): {e}")
            return ""

    @staticmethod
    def pdf_to_images(pdf_path, output_dir="temp_images"):
        """Convert each page of PDF to images and return list of image paths"""
        os.makedirs(output_dir, exist_ok=True)
        doc = fitz.open(pdf_path)
        image_paths = []
        for i, page in enumerate(doc):
            pix = page.get_pixmap()
            img_path = os.path.join(output_dir, f"page_{i + 1}.png")
            pix.save(img_path)
            image_paths.append(img_path)
        return image_paths

    def extract_text_from_file(self, file_path: str, save_output: bool = False):
        """Extract text from a PDF or image file"""
        all_text = ""

        if file_path.lower().endswith(".pdf"):
            image_paths = self.pdf_to_images(file_path)
            for idx, img_path in enumerate(image_paths):
                img = Image.open(img_path).convert("RGB")
                text = self.extract_document_text_smart(img, page_num=idx)
                all_text += f"\n\n--- Page {idx + 1} ---\n\n{text}"
        else:
            img = Image.open(file_path).convert("RGB")
            all_text = self.extract_document_text_smart(img, page_num=0)

        if save_output:
            out_path = os.path.splitext(file_path)[0] + "_ocr.txt"
            with open(out_path, "w", encoding="utf-8") as f:
                f.write(all_text)
            print(f"[INFO] 💾 OCR output saved to: {out_path}")

        return all_text


if __name__ == "__main__":
    model_path = "model/d_ocr_model/snapshots/3baad182cc87c65a1861f0c30357d3467e978172"
    ocr_engine = DocumentOCR(model_path)

    file_path = "input/image_5.webp"
    result = ocr_engine.extract_text_from_file(file_path, save_output=True)

    print("\n[FINAL OCR OUTPUT PREVIEW]\n")
    print(result)
