import os
import time
import torch
import fitz  # PyMuPDF
from PIL import Image
from flask import Flask, request, jsonify
from transformers import <PERSON>Tokenizer, AutoProcessor, AutoModelForImageTextToText

# ==============================
# OCR Engine Class
# ==============================
class DocumentOCR:
    def __init__(self, model_path: str, use_cuda: bool = True):
        """Initialize OCR model"""
        self.device = torch.device("cuda" if use_cuda and torch.cuda.is_available() else "cpu")
        print(f"🔧 Using device: {self.device}")

        self.model = AutoModelForImageTextToText.from_pretrained(
            model_path,
            torch_dtype=torch.float16 if self.device.type == "cuda" else torch.float32,
            device_map=None
        ).to(self.device)
        self.model.eval()

        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.processor = AutoProcessor.from_pretrained(model_path)

    def extract_document_text_smart(self, image: Image.Image, page_num: int = 0) -> str:
        """Enhanced OCR for documents"""
        try:
            start_time = time.time()
            print(f"\n[INFO] OCR processing page {page_num + 1}...")

            prompt = (
                "Extract all visible text from this CMD terminal screenshot exactly as it appears. "
                "Preserve indentation, line breaks, symbols, and formatting. Do not summarize. "
                "Return only the raw plain text output. Do not add extra notes or explanations."
            )

            messages = [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": [
                    {"type": "image", "image": image},
                    {"type": "text", "text": prompt},
                ]},
            ]

            text = self.processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
            inputs = self.processor(
                text=[text], images=[image], padding=True, return_tensors="pt"
            ).to(self.model.device)

            output_ids = self.model.generate(**inputs, max_new_tokens=15000, do_sample=False)
            generated_ids = [output_ids[len(input_ids):] for input_ids, output_ids in zip(inputs.input_ids, output_ids)]
            output_text = self.processor.batch_decode(generated_ids, skip_special_tokens=True, clean_up_tokenization_spaces=True)

            execution_time = time.time() - start_time
            print(f"[INFO] ✅ OCR completed in {execution_time:.2f} seconds")

            return output_text[0]

        except Exception as e:
            print(f"❌ Error in OCR (page {page_num + 1}): {e}")
            return ""

    @staticmethod
    def pdf_to_images(pdf_path, output_dir="temp_images"):
        """Convert each page of PDF to images and return list of image paths"""
        os.makedirs(output_dir, exist_ok=True)
        doc = fitz.open(pdf_path)
        image_paths = []
        for i, page in enumerate(doc):
            pix = page.get_pixmap()
            img_path = os.path.join(output_dir, f"page_{i + 1}.png")
            pix.save(img_path)
            image_paths.append(img_path)
        return image_paths

    def extract_text_from_file(self, file_path: str):
        all_text = ""
        ext = file_path.lower().split(".")[-1]

        if ext == "pdf":
            image_paths = self.pdf_to_images(file_path)
            for idx, img_path in enumerate(image_paths):
                img = Image.open(img_path).convert("RGB")
                text = self.extract_document_text_smart(img, page_num=idx)
                all_text += f"\n\n--- Page {idx + 1} ---\n\n{text}"
        else:
            try:
                # Load and normalize image format
                img = Image.open(file_path).convert("RGB")
                if ext not in ["jpg", "jpeg", "png"]:
                    # Convert to JPG before OCR
                    converted_path = file_path + ".jpg"
                    img.save(converted_path, "JPEG")
                    print(f"[INFO] Converted {file_path} -> {converted_path}")
                    file_path = converted_path
                    img = Image.open(file_path).convert("RGB")

                all_text = self.extract_document_text_smart(img, page_num=0)

            except Exception as e:
                print(f"❌ Error loading image: {e}")
                all_text = f"❌ Error: Unsupported or corrupted image file ({file_path})"

        return all_text


# ==============================
# Flask API
# ==============================
app = Flask(__name__)

# Load model once at startup
model_path = "./model/d_ocr_model/snapshots/3baad182cc87c65a1861f0c30357d3467e978172"
ocr_engine = DocumentOCR(model_path)


@app.route("/ocr", methods=["POST"])
def ocr_endpoint():
    """
    Upload a file (PDF/Image) and return extracted text
    """
    if "file" not in request.files:
        return jsonify({"error": "No file uploaded"}), 400

    file = request.files["file"]
    if file.filename == "":
        return jsonify({"error": "Empty filename"}), 400

    # Save uploaded file temporarily
    temp_path = os.path.join("uploads", file.filename)
    os.makedirs("uploads", exist_ok=True)
    file.save(temp_path)

    # Run OCR
    extracted_text = ocr_engine.extract_text_from_file(temp_path)

    return jsonify({
        "filename": file.filename,
        "extracted_text": extracted_text
    })


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=6000)
