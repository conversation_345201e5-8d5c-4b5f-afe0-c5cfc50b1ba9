import torch
import time
from PIL import Image
from transformers import <PERSON>Tokenizer, AutoProcessor, AutoModelForImageTextToText
import fitz  # PyMuPDF for PDF handling
import os
import re


class DocumentOCR:
    def __init__(self, model_path: str, use_cuda: bool = True):
        """Initialize OCR model"""
        self.device = torch.device("cuda" if use_cuda and torch.cuda.is_available() else "cpu")
        print(f"🔧 Using device: {self.device}")

        self.model = AutoModelForImageTextToText.from_pretrained(
            model_path,
            torch_dtype=torch.float16 if self.device.type == "cuda" else torch.float32,
            device_map=None,
            local_files_only=True,
            trust_remote_code=True
        ).to(self.device)
        self.model.eval()

        self.tokenizer = AutoTokenizer.from_pretrained(model_path, local_files_only=True, trust_remote_code=True)
        self.processor = AutoProcessor.from_pretrained(model_path, local_files_only=True, trust_remote_code=True)

    def clean_html_tags(self, text: str) -> str:
        """Remove any HTML/XML tags that might appear in the output"""
        # Remove common HTML table tags
        html_patterns = [
            r'</?table[^>]*>',
            r'</?thead[^>]*>',
            r'</?tbody[^>]*>',
            r'</?tr[^>]*>',
            r'</?td[^>]*>',
            r'</?th[^>]*>',
            r'</?div[^>]*>',
            r'</?span[^>]*>',
            r'</?p[^>]*>',
            r'</?br[^>]*>',
            r'</?ul[^>]*>',
            r'</?ol[^>]*>',
            r'</?li[^>]*>',
            r'</?h[1-6][^>]*>',
            r'</?strong[^>]*>',
            r'</?em[^>]*>',
            r'</?b[^>]*>',
            r'</?i[^>]*>',
            r'<[^>]+>'  # Catch any remaining tags
        ]

        cleaned_text = text
        for pattern in html_patterns:
            cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.IGNORECASE)

        return cleaned_text

    def format_table_text(self, text: str) -> str:
        """Format text to ensure proper table alignment using spaces"""
        lines = text.split('\n')
        formatted_lines = []

        # Simple approach: look for table data patterns and format them
        i = 0
        in_table = False

        while i < len(lines):
            line = lines[i].strip()

            # Skip empty lines
            if not line:
                formatted_lines.append('')
                i += 1
                continue

            # Detect table start
            if not in_table and line.lower() == 'description':
                # Look ahead to see if next few lines are table headers
                if (i + 3 < len(lines) and
                    lines[i + 1].strip().lower() == 'qty' and
                    lines[i + 2].strip().lower() == 'price' and
                    lines[i + 3].strip().lower() == 'total'):

                    in_table = True
                    # Add formatted table header
                    formatted_lines.append(f"{'Description':<30} {'Qty':>5} {'Price':>12} {'Total':>12}")
                    i += 4  # Skip the individual header lines
                    continue

            # Process table data rows
            if in_table:
                # Look for table data pattern: description, qty, price, total in sequence
                if (i + 3 < len(lines) and
                    lines[i + 1].strip().isdigit() and  # qty is a number
                    '$' in lines[i + 2].strip() and     # price has $
                    '$' in lines[i + 3].strip()):       # total has $

                    description = line
                    qty = lines[i + 1].strip()
                    price = lines[i + 2].strip()
                    total = lines[i + 3].strip()

                    formatted_row = f"{description:<30} {qty:>5} {price:>12} {total:>12}"
                    formatted_lines.append(formatted_row)
                    i += 4  # Skip the processed lines
                    continue
                else:
                    # End of table
                    in_table = False

            # Regular line
            formatted_lines.append(line)
            i += 1

        return '\n'.join(formatted_lines)

    def preserve_spatial_layout(self, text: str) -> str:
        """Preserve spatial layout by detecting and formatting side-by-side content"""
        lines = text.split('\n')
        formatted_lines = []

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # Skip empty lines
            if not line:
                formatted_lines.append('')
                i += 1
                continue

            # Detect potential side-by-side content patterns
            # Look for invoice-specific patterns that should be side-by-side
            if self._is_invoice_header_section(lines, i):
                # Process invoice header section with side-by-side layout
                processed_lines, next_i = self._format_invoice_header_section(lines, i)
                formatted_lines.extend(processed_lines)
                i = next_i
                continue

            # Regular line processing
            formatted_lines.append(line)
            i += 1

        return '\n'.join(formatted_lines)

    def _is_invoice_header_section(self, lines: list, start_idx: int) -> bool:
        """Check if this section contains invoice header information that should be side-by-side"""
        # Look ahead for invoice-related keywords
        section_text = ' '.join(lines[start_idx:min(start_idx + 10, len(lines))]).lower()

        # Check for invoice header patterns
        invoice_keywords = ['invoice no', 'invoice date', 'bill to', 'seller', 'address', 'phone', 'mail']
        keyword_count = sum(1 for keyword in invoice_keywords if keyword in section_text)

        return keyword_count >= 3  # If we find multiple invoice-related keywords

    def _format_invoice_header_section(self, lines: list, start_idx: int) -> tuple:
        """Format invoice header section with proper side-by-side layout"""
        formatted_lines = []
        i = start_idx

        # Collect all lines in this section
        section_lines = []
        while i < len(lines):
            line = lines[i].strip()
            if not line:
                i += 1
                continue

            # Stop if we hit table or other content
            if any(keyword in line.lower() for keyword in ['description', 'quantity', 'price', 'total', 'subtotal', 'notes']):
                break

            section_lines.append(line)
            i += 1

            # Limit section size
            if len(section_lines) > 15:
                break

        # Separate content into different sections
        title_lines = []
        seller_section = []
        bill_to_section = []
        invoice_details = []

        current_section = None

        for line in section_lines:
            line_lower = line.lower()

            # Invoice details (right column)
            if any(keyword in line_lower for keyword in ['invoice no', 'invoice date']):
                invoice_details.append(line)
                current_section = 'invoice'
            # Seller section
            elif 'seller' in line_lower:
                seller_section.append(line)
                current_section = 'seller'
            # Bill To section
            elif 'bill to' in line_lower:
                bill_to_section.append(line)
                current_section = 'bill_to'
            # Company name or title (centered)
            elif line.isupper() or ('construction' in line_lower and len(line) < 30) or \
                 ('company' in line_lower and len(line) < 30) or line_lower == 'invoice':
                title_lines.append(line)
                current_section = None
            # Address, mail, phone - assign to current section
            elif any(keyword in line_lower for keyword in ['address', 'mail', 'phone']) and current_section:
                if current_section == 'seller':
                    seller_section.append(line)
                elif current_section == 'bill_to':
                    bill_to_section.append(line)
                elif current_section == 'invoice':
                    invoice_details.append(line)
            else:
                # Default assignment based on context
                if current_section == 'seller':
                    seller_section.append(line)
                elif current_section == 'bill_to':
                    bill_to_section.append(line)
                elif current_section == 'invoice':
                    invoice_details.append(line)
                else:
                    # If no current section, try to determine from content
                    if any(keyword in line_lower for keyword in ['construction', 'company']):
                        seller_section.append(line)
                    else:
                        title_lines.append(line)

        # Add title lines first (centered)
        for title in title_lines:
            formatted_lines.append(title)

        if formatted_lines:
            formatted_lines.append('')  # Add spacing after title

        # Create side-by-side layout for seller info and invoice details
        max_lines = max(len(seller_section), len(invoice_details))

        for j in range(max_lines):
            left_text = seller_section[j] if j < len(seller_section) else ""
            right_text = invoice_details[j] if j < len(invoice_details) else ""

            if left_text and right_text:
                # Format side-by-side with proper spacing
                formatted_line = f"{left_text:<50}{right_text}"
                formatted_lines.append(formatted_line)
            elif left_text:
                formatted_lines.append(left_text)
            elif right_text:
                # Right-align by adding leading spaces
                formatted_line = f"{'':<50}{right_text}"
                formatted_lines.append(formatted_line)

        # Add Bill To section separately (usually below)
        if bill_to_section:
            formatted_lines.append('')  # Add spacing
            for bill_line in bill_to_section:
                formatted_lines.append(bill_line)

        return formatted_lines, i

    def detect_document_layout(self, text: str) -> str:
        """Detect document type and apply appropriate spatial layout"""
        text_lower = text.lower()

        # Detect document type
        if any(keyword in text_lower for keyword in ['invoice', 'bill to', 'invoice no']):
            return self._format_invoice_layout(text)
        elif any(keyword in text_lower for keyword in ['receipt', 'total', 'subtotal']):
            return self._format_receipt_layout(text)
        else:
            return self._format_general_layout(text)

    def _format_invoice_layout(self, text: str) -> str:
        """Specialized formatting for invoice documents"""
        return self.preserve_spatial_layout(text)

    def _format_receipt_layout(self, text: str) -> str:
        """Specialized formatting for receipt documents"""
        # For receipts, usually simpler layout - mostly single column
        return text

    def _format_general_layout(self, text: str) -> str:
        """General layout formatting for other document types"""
        lines = text.split('\n')
        formatted_lines = []

        for line in lines:
            line = line.strip()
            if line:
                # Basic formatting - preserve indentation and spacing
                formatted_lines.append(line)
            else:
                formatted_lines.append('')

        return '\n'.join(formatted_lines)

    def extract_document_text_smart(self, image: Image.Image, page_num: int = 0) -> str:
        """Enhanced OCR for documents"""
        try:
            start_time = time.time()
            print(f"\n[INFO] OCR processing page {page_num + 1}...")

            prompt = (
                "Extract ALL visible text from this document image EXACTLY as it appears with SPATIAL LAYOUT PRESERVED. "
                "CRITICAL REQUIREMENTS:\n"
                "1. Output ONLY plain text - absolutely NO HTML, XML, JSON, or any markup tags\n"
                "2. PRESERVE SPATIAL POSITIONING: If text appears side-by-side, format it side-by-side using spaces\n"
                "3. MAINTAIN COLUMN LAYOUT: Use consistent spacing to keep text in their relative positions\n"
                "4. For text in RIGHT COLUMN (like invoice details), add appropriate leading spaces to position it on the right\n"
                "5. For text in LEFT COLUMN (like seller info), keep it left-aligned\n"
                "6. For tables: align columns using spaces like monospace text\n"
                "7. Do NOT summarize, interpret, or reformat anything\n"
                "8. Reproduce the EXACT spatial layout as it appears in the image\n"
                "9. Use spaces to maintain the visual structure and positioning of all text elements"
            )

            messages = [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": [
                    {"type": "image", "image": image},
                    {"type": "text", "text": prompt},
                ]},
            ]

            text = self.processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
            inputs = self.processor(
                text=[text], images=[image], padding=True, return_tensors="pt"
            ).to(self.model.device)

            output_ids = self.model.generate(**inputs, max_new_tokens=15000, do_sample=False)
            generated_ids = [output_ids[len(input_ids):] for input_ids, output_ids in zip(inputs.input_ids, output_ids)]
            output_text = self.processor.batch_decode(generated_ids, skip_special_tokens=True, clean_up_tokenization_spaces=True)

            # Post-process the output to ensure clean plain text with spatial layout preservation
            raw_text = output_text[0]

            # Step 1: Remove any HTML/XML tags
            cleaned_text = self.clean_html_tags(raw_text)

            # Step 2: Detect document type and preserve spatial layout
            spatial_text = self.detect_document_layout(cleaned_text)

            # Step 3: Format table-like content for better alignment
            formatted_text = self.format_table_text(spatial_text)

            # Step 4: Clean up excessive whitespace while preserving intentional spacing
            lines = formatted_text.split('\n')
            processed_lines = []
            for line in lines:
                # Remove trailing whitespace but preserve leading whitespace (indentation)
                processed_line = line.rstrip()
                processed_lines.append(processed_line)

            final_text = '\n'.join(processed_lines)

            # Remove excessive consecutive empty lines (more than 2)
            final_text = re.sub(r'\n{4,}', '\n\n\n', final_text)

            execution_time = time.time() - start_time
            print(f"[INFO] ✅ OCR completed in {execution_time:.2f} seconds")

            return final_text

        except Exception as e:
            print(f"❌ Error in OCR (page {page_num + 1}): {e}")
            return ""

    @staticmethod
    def pdf_to_images(pdf_path, output_dir="temp_images"):
        """Convert each page of PDF to images and return list of image paths"""
        os.makedirs(output_dir, exist_ok=True)
        doc = fitz.open(pdf_path)
        image_paths = []
        for i, page in enumerate(doc):
            pix = page.get_pixmap()
            img_path = os.path.join(output_dir, f"page_{i + 1}.png")
            pix.save(img_path)
            image_paths.append(img_path)
        return image_paths

    def extract_text_from_file(self, file_path: str, save_output: bool = False):
        """Extract text from a PDF or image file"""
        all_text = ""

        if file_path.lower().endswith(".pdf"):
            image_paths = self.pdf_to_images(file_path)
            for idx, img_path in enumerate(image_paths):
                img = Image.open(img_path).convert("RGB")
                text = self.extract_document_text_smart(img, page_num=idx)
                all_text += f"\n\n--- Page {idx + 1} ---\n\n{text}"
        else:
            img = Image.open(file_path).convert("RGB")
            all_text = self.extract_document_text_smart(img, page_num=0)

        if save_output:
            out_path = os.path.splitext(file_path)[0] + "_ocr.txt"
            with open(out_path, "w", encoding="utf-8") as f:
                f.write(all_text)
            print(f"[INFO] 💾 OCR output saved to: {out_path}")

        return all_text


if __name__ == "__main__":
    model_path = "model/d_ocr_model/snapshots/3baad182cc87c65a1861f0c30357d3467e978172"
    ocr_engine = DocumentOCR(model_path)

    file_path = "input/image_4.webp"
    result = ocr_engine.extract_text_from_file(file_path, save_output=True)

    print("\n[FINAL OCR OUTPUT PREVIEW]\n")
    print(result)
