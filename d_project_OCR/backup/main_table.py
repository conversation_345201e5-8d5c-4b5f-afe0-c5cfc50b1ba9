import torch
import time
from PIL import Image
from transformers import <PERSON>Tokenizer, AutoProcessor, AutoModelForImageTextToText
import fitz  # PyMuPDF for PDF handling
import os
import re


class DocumentOCR:
    def __init__(self, model_path: str, use_cuda: bool = True):
        """Initialize OCR model"""
        self.device = torch.device("cuda" if use_cuda and torch.cuda.is_available() else "cpu")
        print(f"🔧 Using device: {self.device}")

        self.model = AutoModelForImageTextToText.from_pretrained(
            model_path,
            torch_dtype=torch.float16 if self.device.type == "cuda" else torch.float32,
            device_map=None,
            local_files_only=True,
            trust_remote_code=True
        ).to(self.device)
        self.model.eval()

        self.tokenizer = AutoTokenizer.from_pretrained(model_path, local_files_only=True, trust_remote_code=True)
        self.processor = AutoProcessor.from_pretrained(model_path, local_files_only=True, trust_remote_code=True)

    def clean_html_tags(self, text: str) -> str:
        """Remove any HTML/XML tags that might appear in the output"""
        # Remove common HTML table tags
        html_patterns = [
            r'</?table[^>]*>',
            r'</?thead[^>]*>',
            r'</?tbody[^>]*>',
            r'</?tr[^>]*>',
            r'</?td[^>]*>',
            r'</?th[^>]*>',
            r'</?div[^>]*>',
            r'</?span[^>]*>',
            r'</?p[^>]*>',
            r'</?br[^>]*>',
            r'</?ul[^>]*>',
            r'</?ol[^>]*>',
            r'</?li[^>]*>',
            r'</?h[1-6][^>]*>',
            r'</?strong[^>]*>',
            r'</?em[^>]*>',
            r'</?b[^>]*>',
            r'</?i[^>]*>',
            r'<[^>]+>'  # Catch any remaining tags
        ]

        cleaned_text = text
        for pattern in html_patterns:
            cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.IGNORECASE)

        return cleaned_text

    def format_table_text(self, text: str) -> str:
        """Format text to ensure proper table alignment using spaces"""
        lines = text.split('\n')
        formatted_lines = []

        # Simple approach: look for table data patterns and format them
        i = 0
        in_table = False

        while i < len(lines):
            line = lines[i].strip()

            # Skip empty lines
            if not line:
                formatted_lines.append('')
                i += 1
                continue

            # Detect table start
            if not in_table and line.lower() == 'description':
                # Look ahead to see if next few lines are table headers
                if (i + 3 < len(lines) and
                    lines[i + 1].strip().lower() == 'qty' and
                    lines[i + 2].strip().lower() == 'price' and
                    lines[i + 3].strip().lower() == 'total'):

                    in_table = True
                    # Add formatted table header
                    formatted_lines.append(f"{'Description':<30} {'Qty':>5} {'Price':>12} {'Total':>12}")
                    i += 4  # Skip the individual header lines
                    continue

            # Process table data rows
            if in_table:
                # Look for table data pattern: description, qty, price, total in sequence
                if (i + 3 < len(lines) and
                    lines[i + 1].strip().isdigit() and  # qty is a number
                    '$' in lines[i + 2].strip() and     # price has $
                    '$' in lines[i + 3].strip()):       # total has $

                    description = line
                    qty = lines[i + 1].strip()
                    price = lines[i + 2].strip()
                    total = lines[i + 3].strip()

                    formatted_row = f"{description:<30} {qty:>5} {price:>12} {total:>12}"
                    formatted_lines.append(formatted_row)
                    i += 4  # Skip the processed lines
                    continue
                else:
                    # End of table
                    in_table = False

            # Regular line
            formatted_lines.append(line)
            i += 1

        return '\n'.join(formatted_lines)

    def extract_document_text_smart(self, image: Image.Image, page_num: int = 0) -> str:
        """Enhanced OCR for documents"""
        try:
            start_time = time.time()
            print(f"\n[INFO] OCR processing page {page_num + 1}...")

            prompt = (
                "Extract ALL visible text from this document image EXACTLY as it appears. "
                "CRITICAL REQUIREMENTS:\n"
                "1. Output ONLY plain text - absolutely NO HTML, XML, JSON, or any markup tags\n"
                "2. Preserve ALL spacing, indentation, and line breaks exactly as shown\n"
                "3. For tables: align columns using spaces like monospace text\n"
                "4. Keep headers and data rows properly aligned in columns\n"
                "5. Do NOT summarize, interpret, or reformat anything\n"
                "6. Reproduce the raw text layout exactly as it appears\n"
                "7. Use spaces (not tabs) to maintain column alignment\n"
                "8. If you see a table, format it like this example:\n"
                "   Description                    Qty    Price     Total\n"
                "   Item 1                          1    $10.00    $10.00\n"
                "   Item 2                          2    $15.00    $30.00\n"
                "Output the text exactly as you see it, with proper spacing for readability."
            )

            messages = [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": [
                    {"type": "image", "image": image},
                    {"type": "text", "text": prompt},
                ]},
            ]

            text = self.processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
            inputs = self.processor(
                text=[text], images=[image], padding=True, return_tensors="pt"
            ).to(self.model.device)

            output_ids = self.model.generate(**inputs, max_new_tokens=15000, do_sample=False)
            generated_ids = [output_ids[len(input_ids):] for input_ids, output_ids in zip(inputs.input_ids, output_ids)]
            output_text = self.processor.batch_decode(generated_ids, skip_special_tokens=True, clean_up_tokenization_spaces=True)

            # Post-process the output to ensure clean plain text
            raw_text = output_text[0]

            # Step 1: Remove any HTML/XML tags
            cleaned_text = self.clean_html_tags(raw_text)

            # Step 2: Format table-like content for better alignment
            formatted_text = self.format_table_text(cleaned_text)

            # Step 3: Clean up excessive whitespace while preserving intentional spacing
            lines = formatted_text.split('\n')
            processed_lines = []
            for line in lines:
                # Remove trailing whitespace but preserve leading whitespace (indentation)
                processed_line = line.rstrip()
                processed_lines.append(processed_line)

            final_text = '\n'.join(processed_lines)

            # Remove excessive consecutive empty lines (more than 2)
            final_text = re.sub(r'\n{4,}', '\n\n\n', final_text)

            execution_time = time.time() - start_time
            print(f"[INFO] ✅ OCR completed in {execution_time:.2f} seconds")

            return final_text

        except Exception as e:
            print(f"❌ Error in OCR (page {page_num + 1}): {e}")
            return ""

    @staticmethod
    def pdf_to_images(pdf_path, output_dir="temp_images"):
        """Convert each page of PDF to images and return list of image paths"""
        os.makedirs(output_dir, exist_ok=True)
        doc = fitz.open(pdf_path)
        image_paths = []
        for i, page in enumerate(doc):
            pix = page.get_pixmap()
            img_path = os.path.join(output_dir, f"page_{i + 1}.png")
            pix.save(img_path)
            image_paths.append(img_path)
        return image_paths

    def extract_text_from_file(self, file_path: str, save_output: bool = False):
        """Extract text from a PDF or image file"""
        all_text = ""

        if file_path.lower().endswith(".pdf"):
            image_paths = self.pdf_to_images(file_path)
            for idx, img_path in enumerate(image_paths):
                img = Image.open(img_path).convert("RGB")
                text = self.extract_document_text_smart(img, page_num=idx)
                all_text += f"\n\n--- Page {idx + 1} ---\n\n{text}"
        else:
            img = Image.open(file_path).convert("RGB")
            all_text = self.extract_document_text_smart(img, page_num=0)

        if save_output:
            out_path = os.path.splitext(file_path)[0] + "_ocr.txt"
            with open(out_path, "w", encoding="utf-8") as f:
                f.write(all_text)
            print(f"[INFO] 💾 OCR output saved to: {out_path}")

        return all_text


if __name__ == "__main__":
    model_path = "model/d_ocr_model/snapshots/3baad182cc87c65a1861f0c30357d3467e978172"
    ocr_engine = DocumentOCR(model_path)

    file_path = "input/image_4.webp"
    result = ocr_engine.extract_text_from_file(file_path, save_output=True)

    print("\n[FINAL OCR OUTPUT PREVIEW]\n")
    print(result)
